package com.tvplayer.webdav.ui.main

import android.os.Bundle
import androidx.leanback.app.BrowseSupportFragment
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.HeaderItem
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.ListRowPresenter
import androidx.leanback.widget.Presenter
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.tvplayer.webdav.R

class MainBrowseFragment : BrowseSupportFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupUIElements()
        loadRows()
    }

    private fun setupUIElements() {
        title = getString(R.string.browse_title)
        headersState = HEADERS_ENABLED
        isHeadersTransitionOnBackEnabled = true

        // 设置背景色
        brandColor = resources.getColor(R.color.background_color, null)
        searchAffordanceColor = resources.getColor(R.color.accent_color, null)
    }

    private fun loadRows() {
        val rowsAdapter = ArrayObjectAdapter(ListRowPresenter())
        val mediaItemPresenter = MediaItemPresenter()

        // 示例行：播放历史
        val historyHeader = HeaderItem(0L, "播放历史")
        val historyList = ArrayObjectAdapter(mediaItemPresenter)
        historyList.add(MediaItem("1", "电影A", posterPath = "https://image.tmdb.org/t/p/w500/kqjL17yufvn9OVLyXYpvcoRPUMs.jpg", mediaType = MediaType.MOVIE))
        historyList.add(MediaItem("2", "电影B", posterPath = "https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7EFY0lHR9.jpg", mediaType = MediaType.MOVIE))
        rowsAdapter.add(ListRow(historyHeader, historyList))

        // 示例行：电影
        val moviesHeader = HeaderItem(1L, "电影")
        val moviesList = ArrayObjectAdapter(mediaItemPresenter)
        moviesList.add(MediaItem("3", "电影C", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.MOVIE))
        moviesList.add(MediaItem("4", "电影D", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.MOVIE))
        rowsAdapter.add(ListRow(moviesHeader, moviesList))

        // 示例行：电视剧
        val tvShowsHeader = HeaderItem(2L, "电视剧")
        val tvShowsList = ArrayObjectAdapter(mediaItemPresenter)
        tvShowsList.add(MediaItem("5", "电视剧E", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.TV_SERIES))
        tvShowsList.add(MediaItem("6", "电视剧F", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.TV_SERIES))
        rowsAdapter.add(ListRow(tvShowsHeader, tvShowsList))

        adapter = rowsAdapter

        setOnItemViewClickedListener { itemViewHolder, item, rowViewHolder, row ->
            if (item is MediaItem) {
                val intent = android.content.Intent(activity, com.tvplayer.webdav.ui.details.VideoDetailsActivity::class.java)
                intent.putExtra("media_item", item)
                startActivity(intent)
            }
        }
    }

}

