package com.tvplayer.webdav.ui.details

import android.os.Bundle
import androidx.leanback.app.DetailsSupportFragment
import androidx.leanback.widget.Action
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ClassPresenterSelector
import androidx.leanback.widget.DetailsOverviewRow
import androidx.leanback.widget.DetailsOverviewRowPresenter
import androidx.leanback.widget.FullWidthDetailsOverviewRowPresenter
import androidx.leanback.widget.HeaderItem
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.ListRowPresenter
import androidx.leanback.widget.Presenter
import androidx.core.content.ContextCompat
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.tvplayer.webdav.R
import com.tvplayer.webdav.data.model.MediaItem
import com.tvplayer.webdav.data.model.MediaType
import com.tvplayer.webdav.ui.main.MediaItemPresenter

class VideoDetailsSupportFragment : DetailsSupportFragment() {

    private lateinit var mSelectedMediaItem: MediaItem
    private lateinit var mDetailsOverviewRowPresenter: FullWidthDetailsOverviewRowPresenter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mSelectedMediaItem = activity?.intent?.getParcelableExtra("mediaItem") ?: MediaItem()

        setupDetailsFragment()
    }

    private fun setupDetailsFragment() {
        val selector = ClassPresenterSelector()
        mDetailsOverviewRowPresenter = FullWidthDetailsOverviewRowPresenter(DetailsDescriptionPresenter())

        // 设置操作按钮的点击监听器
        mDetailsOverviewRowPresenter.onActionClickedListener = 
            androidx.leanback.widget.OnActionClickedListener { action ->
                if (action.id == ACTION_PLAY) {
                    // TODO: Implement playback logic
                    android.widget.Toast.makeText(activity, "播放: ${mSelectedMediaItem.getDisplayTitle()}", android.widget.Toast.LENGTH_SHORT).show()
                }
            }

        selector.addClassPresenter(DetailsOverviewRow::class.java, mDetailsOverviewRowPresenter)
        selector.addClassPresenter(ListRow::class.java, ListRowPresenter())
        adapter = ArrayObjectAdapter(selector)

        buildDetails()
    }

    private fun buildDetails() {
        val detailsOverviewRow = DetailsOverviewRow(mSelectedMediaItem)

        // 加载背景图片
        val backdropUrl = mSelectedMediaItem.backdropPath ?: mSelectedMediaItem.posterPath
        if (!backdropUrl.isNullOrEmpty()) {
            Glide.with(activity!!)
                .asBitmap()
                .load(backdropUrl)
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(bitmap: Bitmap, transition: Transition<in Bitmap>?) {
                        detailsOverviewRow.imageDrawable = BitmapDrawable(resources, bitmap)
                        adapter.notifyArrayItemRangeChanged(0, 1)
                    }

                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        detailsOverviewRow.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.ic_video)
                        adapter.notifyArrayItemRangeChanged(0, 1)
                    }
                })
        } else {
            detailsOverviewRow.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.ic_video)
        }

        val actionAdapter = ArrayObjectAdapter()
        actionAdapter.add(Action(ACTION_PLAY, "播放"))
        detailsOverviewRow.actionsAdapter = actionAdapter

        val rowsAdapter = adapter as ArrayObjectAdapter
        rowsAdapter.add(detailsOverviewRow)

        // 添加剧集列表 (如果mediaType是TV_SERIES)
        if (mSelectedMediaItem.mediaType == MediaType.TV_SERIES) {
            val episodesHeader = HeaderItem(1L, "剧集")
            val episodesList = ArrayObjectAdapter(MediaItemPresenter())
            episodesList.add(MediaItem("7", "剧集1", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.TV_EPISODE))
            episodesList.add(MediaItem("8", "剧集2", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.TV_EPISODE))
            rowsAdapter.add(ListRow(episodesHeader, episodesList))
        }

        // 添加相关演员列表
        val actorsHeader = HeaderItem(2L, "相关演员")
        val actorsList = ArrayObjectAdapter(MediaItemPresenter())
        actorsList.add(MediaItem("9", "演员A", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.OTHER))
        actorsList.add(MediaItem("10", "演员B", posterPath = "https://image.tmdb.org/t/p/w500/rMvYgLwYj3f90j0b1Q2Z1X0Y0Y0.jpg", mediaType = MediaType.OTHER))
        rowsAdapter.add(ListRow(actorsHeader, actorsList))
    }

    private class DetailsDescriptionPresenter : DetailsOverviewRowPresenter() {
        override fun onCreateViewHolder(parent: ViewGroup): Presenter.ViewHolder {
            val viewHolder = super.onCreateViewHolder(parent)
            val view = viewHolder.view
            // 可以自定义DetailsOverviewRow的布局，这里使用默认的
            return viewHolder
        }

        override fun onBindViewHolder(viewHolder: Presenter.ViewHolder, item: Any) {
            super.onBindViewHolder(viewHolder, item)
            val mediaItem = (item as DetailsOverviewRow).item as MediaItem
            // 设置标题和描述
            val titleView = viewHolder.view.findViewById<TextView>(R.id.details_overview_title)
            val subtitleView = viewHolder.view.findViewById<TextView>(R.id.details_overview_subtitle)
            val descriptionView = viewHolder.view.findViewById<TextView>(R.id.details_overview_description)

            titleView?.text = mediaItem.getDisplayTitle()
            subtitleView?.text = "${mediaItem.releaseDate?.let { java.text.SimpleDateFormat("yyyy", java.util.Locale.getDefault()).format(it) } ?: ""} · ${mediaItem.genre.joinToString(" · ")}"
            descriptionView?.text = mediaItem.overview
        }
    }

    companion object {
        private const val ACTION_PLAY = 1L
    }
}

