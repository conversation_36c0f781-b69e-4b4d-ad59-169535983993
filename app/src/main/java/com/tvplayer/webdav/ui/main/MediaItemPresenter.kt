package com.tvplayer.webdav.ui.main

import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.leanback.widget.Presenter
import com.bumptech.glide.Glide
import com.tvplayer.webdav.R
import com.tvplayer.webdav.data.model.MediaItem

class MediaItemPresenter : Presenter() {

    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        val view = layoutInflater.inflate(R.layout.item_media_poster, parent, false)
        view.focusable = true
        view.isFocusableInTouchMode = true
        view.setBackgroundResource(R.drawable.tv_remote_focus_selector)
        view.stateListAnimator = parent.context.resources.loadAnimator(R.animator.tv_focus_animator)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, item: Any) {
        val mediaItem = item as MediaItem
        val rootView = viewHolder.view
        val posterImageView = rootView.findViewById<ImageView>(R.id.iv_poster)
        val titleTextView = rootView.findViewById<TextView>(R.id.tv_title)
        val subtitleTextView = rootView.findViewById<TextView>(R.id.tv_subtitle)

        titleTextView.text = mediaItem.getDisplayTitle()
        subtitleTextView.text = mediaItem.getSubtitle()

        Glide.with(viewHolder.view.context)
            .load(mediaItem.posterPath)
            .centerCrop()
            .placeholder(R.drawable.ic_video)
            .error(R.drawable.ic_video)
            .into(posterImageView)
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder) {
        val posterImageView = viewHolder.view.findViewById<ImageView>(R.id.iv_poster)
        Glide.with(viewHolder.view.context).clear(posterImageView)
    }
}

